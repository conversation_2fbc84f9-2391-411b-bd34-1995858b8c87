'use client';

import React, { ReactNode, useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  Menu,
  X,
  Bell,
  ChevronDown,
  LogOut,
  User,
  Store
} from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { SharyouIcon } from '@/components/icons/sharyou-icon';
import { Button } from '@/components/ui/button';

interface DashboardLayoutProps {
  children: ReactNode;
}

const sidebarGroups = [
  {
    id: 'overview',
    label: 'Vue d\'ensemble',
    items: [
      { id: 'home', label: 'Tableau de bord', icon: Home, href: '/dashboard' },
      { id: 'analytics', label: 'Analytics', icon: BarChart3, href: '/dashboard/analytics' },
    ]
  },
  {
    id: 'commerce',
    label: 'Commerce',
    items: [
      { id: 'shop', label: 'Ma Boutique', icon: Store, href: '/dashboard/shop' },
      { id: 'products', label: 'Produits', icon: Package, href: '/dashboard/products' },
      { id: 'orders', label: 'Commandes', icon: ShoppingCart, href: '/dashboard/orders' },
      { id: 'customers', label: 'Clients', icon: Users, href: '/dashboard/customers' },
    ]
  },
  {
    id: 'settings',
    label: 'Configuration',
    items: [
      { id: 'settings', label: 'Paramètres', icon: Settings, href: '/dashboard/settings' },
      { id: 'logout', label: 'Déconnexion', icon: LogOut, href: '#', onClick: true },
    ]
  }
];

const allSidebarItems = sidebarGroups.flatMap(group => group.items);

// Function to get page title based on pathname
const getPageTitle = (pathname: string) => {
  const item = allSidebarItems.find(item => item.href === pathname);
  return item ? item.label : 'Dashboard';
};

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, loading, signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const currentPageTitle = getPageTitle(pathname);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  const handleLogout = async () => {
    await signOut();
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <SharyouIcon size={48} />
        </motion.div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Sidebar */}
      <motion.aside
        className={`fixed inset-y-0 left-0 z-50 bg-white shadow-xl transform transition-all duration-300 ease-in-out ${
          sidebarOpen ? 'translate-x-0 w-64' : '-translate-x-full w-64'
        } lg:translate-x-0 ${
          sidebarExpanded ? 'lg:w-64' : 'lg:w-16'
        }`}
        initial={{ x: -256 }}
        animate={{ x: 0 }}
        transition={{ duration: 0.3 }}
        onMouseEnter={() => setSidebarExpanded(true)}
        onMouseLeave={() => setSidebarExpanded(false)}
      >
        {/* Sidebar Header */}
        <div className={`flex items-center h-16 px-4 transition-all duration-300 ${
          sidebarExpanded ? 'justify-between' : 'lg:justify-center justify-between'
        }`}>
          <div className={`flex items-center transition-all duration-300 ${
            sidebarExpanded ? 'gap-3' : 'lg:gap-0'
          }`}>
            <div className="w-8 h-8 bg-gradient-to-br from-brand-500 to-brand-600 rounded-lg flex items-center justify-center shadow-lg">
              <SharyouIcon size={20} className="text-white" />
            </div>
            <span
              className={`text-xl font-bold bg-gradient-to-r from-brand-600 to-brand-700 bg-clip-text text-transparent transition-all duration-300 ${
                sidebarExpanded ? 'opacity-100 w-auto ml-3' : 'lg:opacity-0 lg:w-0 lg:overflow-hidden lg:hidden lg:ml-0 ml-3'
              }`}
            >
              Sharyou
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden text-neutral-400 hover:text-neutral-600 hover:bg-neutral-100"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 py-4">
          <div className="space-y-4">
            {sidebarGroups.map((group, groupIndex) => (
              <div key={group.id}>
                {/* Group Label */}
                <div className={`px-4 mb-2 transition-all duration-300 ${
                  sidebarExpanded ? 'opacity-100 h-auto' : 'opacity-0 h-0 overflow-hidden'
                }`}>
                  <h3 className="text-xs font-semibold text-neutral-400 uppercase tracking-wide">
                    {group.label}
                  </h3>
                </div>

                {/* Group Items */}
                <div className="space-y-1">
                  {group.items.map((item) => {
                    const isActive = pathname === item.href && item.href !== '#';
                    const isLogout = item.id === 'logout';

                    const handleClick = (e: React.MouseEvent) => {
                      if (isLogout) {
                        e.preventDefault();
                        signOut();
                      }
                    };

                    return (
                      <motion.a
                        key={item.id}
                        href={item.href}
                        onClick={handleClick}
                        className={`flex items-center rounded-lg transition-all duration-300 group relative h-11 ${
                          sidebarExpanded
                            ? 'mx-3 px-3 justify-start'
                            : 'mx-3 px-0 justify-center'
                        } ${
                          isActive
                            ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-md'
                            : isLogout
                            ? 'text-neutral-600 hover:bg-red-50 hover:text-red-600'
                            : 'text-neutral-600 hover:bg-brand-50 hover:text-brand-700'
                        }`}
                        whileHover={{
                          x: isActive ? 0 : (sidebarExpanded ? 1 : 0)
                        }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ duration: 0.15, ease: "easeOut" }}
                        title={!sidebarExpanded ? item.label : ''}
                      >
                        {/* Icon Container - Fixed positioning */}
                        <div className={`flex items-center justify-center w-5 h-5 flex-shrink-0 transition-all duration-300 ${
                          sidebarExpanded ? 'mr-3' : 'mr-0'
                        }`}>
                          <item.icon className={`w-5 h-5 transition-colors duration-200 ${
                            isActive
                              ? 'text-white'
                              : isLogout
                              ? 'text-neutral-500 group-hover:text-red-600'
                              : 'text-neutral-500 group-hover:text-brand-600'
                          }`} />
                        </div>

                        {/* Text Label - Smooth transition */}
                        <span
                          className={`font-medium text-sm whitespace-nowrap transition-all duration-300 ${
                            sidebarExpanded
                              ? 'opacity-100 translate-x-0 w-auto'
                              : 'opacity-0 -translate-x-2 w-0 overflow-hidden'
                          } ${
                            isActive
                              ? 'text-white'
                              : isLogout
                              ? 'text-neutral-700 group-hover:text-red-600'
                              : 'text-neutral-700 group-hover:text-brand-700'
                          }`}
                        >
                          {item.label}
                        </span>

                        {/* Active indicator */}
                        {isActive && (
                          <motion.div
                            className={`absolute w-1 h-1 bg-white rounded-full transition-all duration-300 ${
                              sidebarExpanded ? 'right-3' : 'right-1/2 transform translate-x-1/2'
                            }`}
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.2 }}
                          />
                        )}

                        {/* Tooltip for collapsed state */}
                        <div className={`absolute left-full ml-3 px-3 py-2 bg-neutral-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg ${
                          sidebarExpanded ? 'hidden' : 'hidden lg:block'
                        }`}>
                          {item.label}
                          <div className="absolute left-0 top-1/2 transform -translate-x-1 -translate-y-1/2 w-2 h-2 bg-neutral-900 rotate-45"></div>
                        </div>
                      </motion.a>
                    );
                  })}
                </div>

                {/* Group Separator */}
                {groupIndex < sidebarGroups.length - 1 && (
                  <div className={`mt-4 mx-6 transition-all duration-300 ${
                    sidebarExpanded ? 'opacity-100' : 'opacity-50'
                  }`}>
                    <div className="h-px bg-neutral-200"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </nav>


      </motion.aside>

      {/* Main Content */}
      <div
        className={`transition-all duration-300 ${
          sidebarExpanded ? 'lg:ml-64' : 'lg:ml-16'
        }`}
      >
        {/* Header */}
        <motion.header
          className="bg-white h-16 flex items-center justify-between px-6"
          initial={{ y: -64, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {/* Left Side */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="w-5 h-5" />
            </Button>

            {/* Page Title */}
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-brand-500 to-brand-600 rounded-lg flex items-center justify-center">
                {(() => {
                  const currentItem = allSidebarItems.find(item => item.href === pathname);
                  if (currentItem) {
                    const IconComponent = currentItem.icon;
                    return <IconComponent className="w-4 h-4 text-white" />;
                  }
                  return null;
                })()}
              </div>
              <h1 className="text-xl font-bold text-neutral-900">{currentPageTitle}</h1>
            </div>
          </div>

          {/* Right Side */}
          <div className="flex items-center gap-4">
            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </Button>

            {/* User Menu */}
            <div className="relative">
              <Button
                variant="ghost"
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="flex items-center gap-2"
              >
                <div className="w-8 h-8 bg-brand-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="hidden md:block text-sm font-medium">
                  {user.user_metadata?.first_name || user.email?.split('@')[0]}
                </span>
                <ChevronDown className="w-4 h-4" />
              </Button>

              <AnimatePresence>
                {userMenuOpen && (
                  <motion.div
                    className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 z-50"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors flex items-center gap-2"
                    >
                      <LogOut className="w-4 h-4" />
                      Déconnexion
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.header>

        {/* Page Content */}
        <main className="p-6">
          {children}
        </main>
      </div>

      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
