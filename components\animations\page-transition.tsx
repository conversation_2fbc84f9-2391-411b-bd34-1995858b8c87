'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ReactNode } from 'react';

interface PageTransitionProps {
  children: ReactNode;
  className?: string;
}

// Variants pour les animations de page
const pageVariants = {
  initial: {
    opacity: 0,
    x: 100,
    scale: 0.95,
  },
  in: {
    opacity: 1,
    x: 0,
    scale: 1,
  },
  out: {
    opacity: 0,
    x: -100,
    scale: 0.95,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.6,
};

// Variants pour l'overlay de transition
const overlayVariants = {
  initial: {
    scaleX: 0,
    originX: 0,
  },
  animate: {
    scaleX: 1,
    originX: 0,
    transition: {
      duration: 0.4,
      ease: 'easeInOut',
    },
  },
  exit: {
    scaleX: 0,
    originX: 1,
    transition: {
      duration: 0.4,
      ease: 'easeInOut',
      delay: 0.2,
    },
  },
};

export function PageTransition({ children, className = '' }: PageTransitionProps) {
  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants}
      transition={pageTransition}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Composant pour l'overlay de transition sliding
export function SlidingOverlay() {
  return (
    <motion.div
      variants={overlayVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="fixed inset-0 z-50 bg-gradient-to-r from-brand-500 to-brand-700"
      style={{ transformOrigin: 'left' }}
    />
  );
}

// Hook pour gérer les transitions de navigation
export function usePageTransition() {
  const navigateWithTransition = (navigate: () => void, delay: number = 600) => {
    // Créer l'overlay de transition
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 z-50 bg-gradient-to-r from-brand-500 to-brand-700 transform scale-x-0 origin-left';
    overlay.style.transition = 'transform 0.4s ease-in-out';
    document.body.appendChild(overlay);

    // Animation d'entrée
    requestAnimationFrame(() => {
      overlay.style.transform = 'scaleX(1)';
    });

    // Navigation après l'animation
    setTimeout(() => {
      navigate();
      
      // Animation de sortie
      setTimeout(() => {
        overlay.style.transformOrigin = 'right';
        overlay.style.transform = 'scaleX(0)';
        
        // Nettoyage
        setTimeout(() => {
          if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
          }
        }, 400);
      }, 100);
    }, delay);
  };

  return { navigateWithTransition };
}
