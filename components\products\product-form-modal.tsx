'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Upload, Plus, Minus, Save, Eye, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';

interface ProductVariant {
  id: string;
  name: string;
  values: string[];
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  stock: string;
  sku: string;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  images: File[];
  variants: ProductVariant[];
  isDraft: boolean;
  isActive: boolean;
  taxRate: string;
}

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProductFormData) => Promise<void>;
}

const categories = [
  'Électronique',
  'Vêtements',
  'Maison & Jardin',
  'Sports & Loisirs',
  'Beauté & Santé',
  'Livres',
  'Automobile',
  'Autres'
];

const variantTypes = [
  { id: 'size', name: 'Taille', values: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] },
  { id: 'color', name: 'Couleur', values: ['Rouge', 'Bleu', 'Vert', 'Noir', 'Blanc', 'Jaune'] },
  { id: 'material', name: 'Matériau', values: ['Coton', 'Polyester', 'Laine', 'Soie', 'Lin'] }
];

export function ProductFormModal({ isOpen, onClose, onSubmit }: ProductFormModalProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: '',
    sku: '',
    weight: '',
    dimensions: {
      length: '',
      width: '',
      height: ''
    },
    images: [],
    variants: [],
    isDraft: false,
    isActive: true,
    taxRate: '20'
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'details' | 'variants' | 'preview'>('basic');
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du produit est obligatoire';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Le prix est obligatoire';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Le prix doit être un nombre positif';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est obligatoire';
    }

    if (!formData.category) {
      newErrors.category = 'La catégorie est obligatoire';
    }

    if (formData.stock && (isNaN(Number(formData.stock)) || Number(formData.stock) < 0)) {
      newErrors.stock = 'Le stock doit être un nombre positif ou zéro';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const newImages = [...formData.images, ...files].slice(0, 5);
    
    setFormData(prev => ({ ...prev, images: newImages }));

    const newPreviews = files.map(file => URL.createObjectURL(file));
    setImagePreviews(prev => [...prev, ...newPreviews].slice(0, 5));
  };

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    
    setFormData(prev => ({ ...prev, images: newImages }));
    setImagePreviews(newPreviews);
  };

  const addVariant = (variantType: typeof variantTypes[0]) => {
    const newVariant: ProductVariant = {
      id: Date.now().toString(),
      name: variantType.name,
      values: []
    };
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  const removeVariant = (variantId: string) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter(v => v.id !== variantId)
    }));
  };

  const updateVariantValues = (variantId: string, values: string[]) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map(v => 
        v.id === variantId ? { ...v, values } : v
      )
    }));
  };

  const calculatePriceWithTax = () => {
    const basePrice = Number(formData.price) || 0;
    const taxRate = Number(formData.taxRate) || 0;
    return basePrice + (basePrice * taxRate / 100);
  };

  const handleSubmit = async (isDraft: boolean = false) => {
    if (!isDraft && !validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit({ ...formData, isDraft });
      onClose();
      setFormData({
        name: '',
        description: '',
        price: '',
        category: '',
        stock: '',
        sku: '',
        weight: '',
        dimensions: { length: '', width: '', height: '' },
        images: [],
        variants: [],
        isDraft: false,
        isActive: true,
        taxRate: '20'
      });
      setImagePreviews([]);
      setErrors({});
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-neutral-200">
            <h2 className="text-xl font-semibold text-neutral-900">Ajouter un nouveau produit</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-neutral-200">
            {[
              { id: 'basic', label: 'Informations de base' },
              { id: 'details', label: 'Détails' },
              { id: 'variants', label: 'Variantes' },
              { id: 'preview', label: 'Aperçu' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-brand-600 border-b-2 border-brand-600'
                    : 'text-neutral-600 hover:text-neutral-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {activeTab === 'basic' && (
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom du produit *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Entrez le nom du produit"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie *</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                      <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Sélectionnez une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.category && <p className="text-sm text-red-600">{errors.category}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Décrivez votre produit en détail"
                    rows={4}
                    className={errors.description ? 'border-red-500' : ''}
                  />
                  {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="price">Prix (DA) *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                      placeholder="0.00"
                      className={errors.price ? 'border-red-500' : ''}
                    />
                    {errors.price && <p className="text-sm text-red-600">{errors.price}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="stock">Stock</Label>
                    <Input
                      id="stock"
                      type="number"
                      value={formData.stock}
                      onChange={(e) => setFormData(prev => ({ ...prev, stock: e.target.value }))}
                      placeholder="0"
                      className={errors.stock ? 'border-red-500' : ''}
                    />
                    {errors.stock && <p className="text-sm text-red-600">{errors.stock}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="taxRate">Taux de TVA (%)</Label>
                    <Input
                      id="taxRate"
                      type="number"
                      value={formData.taxRate}
                      onChange={(e) => setFormData(prev => ({ ...prev, taxRate: e.target.value }))}
                      placeholder="20"
                    />
                  </div>
                </div>

                {/* Price calculation */}
                {formData.price && (
                  <div className="bg-brand-50 p-4 rounded-lg">
                    <h4 className="font-medium text-brand-900 mb-2">Calcul du prix</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Prix HT:</span>
                        <span>{Number(formData.price).toFixed(2)} DA</span>
                      </div>
                      <div className="flex justify-between">
                        <span>TVA ({formData.taxRate}%):</span>
                        <span>{((Number(formData.price) * Number(formData.taxRate)) / 100).toFixed(2)} DA</span>
                      </div>
                      <div className="flex justify-between font-medium border-t border-brand-200 pt-1">
                        <span>Prix TTC:</span>
                        <span>{calculatePriceWithTax().toFixed(2)} DA</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Images */}
                <div className="space-y-4">
                  <Label>Images du produit (max 5)</Label>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                    {imagePreviews.map((preview, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={preview}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border border-neutral-200"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                    {imagePreviews.length < 5 && (
                      <label className="w-full h-24 border-2 border-dashed border-neutral-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-brand-500 transition-colors">
                        <div className="text-center">
                          <Upload className="w-6 h-6 text-neutral-400 mx-auto mb-1" />
                          <span className="text-xs text-neutral-500">Ajouter</span>
                        </div>
                        <input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'details' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="sku">SKU / Référence</Label>
                    <Input
                      id="sku"
                      value={formData.sku}
                      onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
                      placeholder="REF-001"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="weight">Poids (kg)</Label>
                    <Input
                      id="weight"
                      type="number"
                      step="0.01"
                      value={formData.weight}
                      onChange={(e) => setFormData(prev => ({ ...prev, weight: e.target.value }))}
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <Label>Dimensions (cm)</Label>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="length">Longueur</Label>
                      <Input
                        id="length"
                        type="number"
                        value={formData.dimensions.length}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          dimensions: { ...prev.dimensions, length: e.target.value }
                        }))}
                        placeholder="0"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="width">Largeur</Label>
                      <Input
                        id="width"
                        type="number"
                        value={formData.dimensions.width}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          dimensions: { ...prev.dimensions, width: e.target.value }
                        }))}
                        placeholder="0"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="height">Hauteur</Label>
                      <Input
                        id="height"
                        type="number"
                        value={formData.dimensions.height}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          dimensions: { ...prev.dimensions, height: e.target.value }
                        }))}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'variants' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-neutral-900">Variantes du produit</h3>
                    <p className="text-sm text-neutral-500">Ajoutez des variantes comme la taille, couleur, etc.</p>
                  </div>
                </div>

                {formData.variants.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Plus className="w-8 h-8 text-neutral-400" />
                    </div>
                    <h4 className="text-lg font-medium text-neutral-900 mb-2">Aucune variante</h4>
                    <p className="text-neutral-500 mb-4">Ajoutez des variantes pour offrir plus d'options à vos clients</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formData.variants.map((variant) => (
                      <div key={variant.id} className="border border-neutral-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-neutral-900">{variant.name}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeVariant(variant.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Minus className="w-4 h-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {variantTypes.find(vt => vt.name === variant.name)?.values.map((value) => (
                            <Badge
                              key={value}
                              variant={variant.values.includes(value) ? "default" : "outline"}
                              className={`cursor-pointer transition-colors ${
                                variant.values.includes(value)
                                  ? 'bg-brand-500 hover:bg-brand-600'
                                  : 'hover:bg-brand-50 hover:text-brand-600'
                              }`}
                              onClick={() => {
                                const newValues = variant.values.includes(value)
                                  ? variant.values.filter(v => v !== value)
                                  : [...variant.values, value];
                                updateVariantValues(variant.id, newValues);
                              }}
                            >
                              {value}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  {variantTypes.map((variantType) => (
                    <Button
                      key={variantType.id}
                      variant="outline"
                      size="sm"
                      onClick={() => addVariant(variantType)}
                      disabled={formData.variants.some(v => v.name === variantType.name)}
                      className="flex items-center gap-2"
                    >
                      <Plus className="w-4 h-4" />
                      Ajouter {variantType.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'preview' && (
              <div className="space-y-6">
                <div className="bg-neutral-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4">Aperçu du produit</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      {imagePreviews.length > 0 ? (
                        <div className="space-y-2">
                          <img
                            src={imagePreviews[0]}
                            alt="Product preview"
                            className="w-full h-48 object-cover rounded-lg border border-neutral-200"
                          />
                          {imagePreviews.length > 1 && (
                            <div className="flex gap-2">
                              {imagePreviews.slice(1, 4).map((preview, index) => (
                                <img
                                  key={index}
                                  src={preview}
                                  alt={`Preview ${index + 2}`}
                                  className="w-16 h-16 object-cover rounded border border-neutral-200"
                                />
                              ))}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="w-full h-48 bg-neutral-200 rounded-lg flex items-center justify-center">
                          <span className="text-neutral-500">Aucune image</span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="text-xl font-semibold text-neutral-900">
                          {formData.name || 'Nom du produit'}
                        </h4>
                        <p className="text-sm text-neutral-500">{formData.category || 'Catégorie'}</p>
                      </div>

                      <div className="text-2xl font-bold text-brand-600">
                        {formData.price ? `${calculatePriceWithTax().toFixed(2)} DA` : '0.00 DA'}
                      </div>

                      <p className="text-neutral-700">
                        {formData.description || 'Description du produit...'}
                      </p>

                      {formData.variants.length > 0 && (
                        <div className="space-y-3">
                          {formData.variants.map((variant) => (
                            <div key={variant.id}>
                              <Label className="text-sm font-medium">{variant.name}</Label>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {variant.values.map((value) => (
                                  <Badge key={value} variant="outline">
                                    {value}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      <div className="flex items-center gap-4 text-sm text-neutral-600">
                        {formData.stock && (
                          <span>Stock: {formData.stock}</span>
                        )}
                        {formData.sku && (
                          <span>SKU: {formData.sku}</span>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant={formData.isActive ? "default" : "secondary"}>
                          {formData.isActive ? 'Actif' : 'Inactif'}
                        </Badge>
                        {formData.isDraft && (
                          <Badge variant="outline">Brouillon</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-neutral-200 bg-neutral-50">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
                <Label>Produit actif</Label>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={onClose}>
                Annuler
              </Button>
              <Button
                variant="outline"
                onClick={() => handleSubmit(true)}
                disabled={loading}
              >
                <FileText className="w-4 h-4 mr-2" />
                Sauvegarder en brouillon
              </Button>
              <Button
                onClick={() => handleSubmit(false)}
                disabled={loading}
                className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {loading ? 'Sauvegarde...' : 'Publier le produit'}
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
