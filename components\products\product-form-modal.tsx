'use client';

import React, { useState, useCallback } from 'react';

import { X, Upload, Plus, Minus, Save, Eye, FileText, Image as ImageIcon, Package, Info, Palette, Hash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';

interface ProductVariant {
  id: string;
  name: string;
  values: string[];
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  stock: string;
  sku: string;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  images: File[];
  variants: ProductVariant[];
  isDraft: boolean;
  isActive: boolean;
}

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProductFormData) => Promise<void>;
}

const categories = [
  'Électronique',
  'Vêtements',
  'Maison & Jardin',
  'Sports & Loisirs',
  'Beauté & Santé',
  'Livres',
  'Automobile',
  'Autres'
];

const variantTypes = [
  { id: 'size', name: 'Taille', values: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] },
  { id: 'color', name: 'Couleur', values: ['Rouge', 'Bleu', 'Vert', 'Noir', 'Blanc', 'Jaune'] },
  { id: 'material', name: 'Matériau', values: ['Coton', 'Polyester', 'Laine', 'Soie', 'Lin'] }
];

export default function ProductFormModal({ isOpen, onClose, onSubmit }: ProductFormModalProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: '',
    sku: '',
    weight: '',
    dimensions: {
      length: '',
      width: '',
      height: ''
    },
    images: [],
    variants: [],
    isDraft: false,
    isActive: true
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'details' | 'variants' | 'preview'>('basic');
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [saveProgress, setSaveProgress] = useState(0);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du produit est obligatoire';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Le nom doit contenir au moins 3 caractères';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Le prix est obligatoire';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Le prix doit être un nombre positif';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est obligatoire';
    } else if (formData.description.length < 10) {
      newErrors.description = 'La description doit contenir au moins 10 caractères';
    }

    if (!formData.category) {
      newErrors.category = 'La catégorie est obligatoire';
    }

    if (formData.stock && (isNaN(Number(formData.stock)) || Number(formData.stock) < 0)) {
      newErrors.stock = 'Le stock doit être un nombre positif ou zéro';
    }

    if (formData.weight && (isNaN(Number(formData.weight)) || Number(formData.weight) < 0)) {
      newErrors.weight = 'Le poids doit être un nombre positif';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleImageUpload = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB max
      return isValidType && isValidSize;
    });

    if (formData.images.length + validFiles.length > 5) {
      setErrors(prev => ({ ...prev, images: 'Vous ne pouvez ajouter que 5 images maximum' }));
      return;
    }

    if (validFiles.length !== fileArray.length) {
      setErrors(prev => ({ ...prev, images: 'Certains fichiers ne sont pas valides (format ou taille)' }));
    }

    const newImages = [...formData.images, ...validFiles];
    setFormData(prev => ({ ...prev, images: newImages }));

    const newPreviews = validFiles.map(file => URL.createObjectURL(file));
    setImagePreviews(prev => [...prev, ...newPreviews]);

    setErrors(prev => ({ ...prev, images: '' }));
  }, [formData.images]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleImageUpload(e.target.files);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    if (e.dataTransfer.files) {
      handleImageUpload(e.dataTransfer.files);
    }
  }, [handleImageUpload]);

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);

    setFormData(prev => ({ ...prev, images: newImages }));
    setImagePreviews(newPreviews);
  };

  const addVariant = (variantType: typeof variantTypes[0]) => {
    const newVariant: ProductVariant = {
      id: Date.now().toString(),
      name: variantType.name,
      values: []
    };
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  const removeVariant = (variantId: string) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter(v => v.id !== variantId)
    }));
  };

  const updateVariantValues = (variantId: string, values: string[]) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map(v => 
        v.id === variantId ? { ...v, values } : v
      )
    }));
  };

  const getFormattedPrice = () => {
    const price = Number(formData.price) || 0;
    return price;
  };

  const handleSubmit = async (isDraft: boolean = false) => {
    if (!isDraft && !validateForm()) {
      return;
    }

    setLoading(true);
    setSaveProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setSaveProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await onSubmit({ ...formData, isDraft });

      clearInterval(progressInterval);
      setSaveProgress(100);

      setTimeout(() => {
        onClose();
        setFormData({
          name: '',
          description: '',
          price: '',
          category: '',
          stock: '',
          sku: '',
          weight: '',
          dimensions: { length: '', width: '', height: '' },
          images: [],
          variants: [],
          isDraft: false,
          isActive: true
        });
        setImagePreviews([]);
        setErrors({});
        setSaveProgress(0);
      }, 500);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setErrors(prev => ({ ...prev, submit: 'Erreur lors de la sauvegarde du produit' }));
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" onClick={onClose}>
        <div
          className="bg-white rounded-xl shadow-brand w-full max-w-5xl max-h-[90vh] overflow-hidden border border-neutral-100"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 bg-gradient-to-r from-brand-50 to-brand-100 border-b border-brand-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-brand-500 rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-display font-semibold text-neutral-900">Ajouter un nouveau produit</h2>
                <p className="text-sm text-neutral-600">Créez et configurez votre produit</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose} className="hover:bg-white/50">
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Progress Bar */}
          {loading && (
            <div className="px-6 py-2 bg-brand-50">
              <div className="flex items-center gap-3">
                <span className="text-sm text-brand-700 font-medium">Sauvegarde en cours...</span>
                <Progress value={saveProgress} className="flex-1 h-2" />
                <span className="text-sm text-brand-600">{saveProgress}%</span>
              </div>
            </div>
          )}

          {/* Tabs */}
          <div className="flex bg-neutral-50 border-b border-neutral-200">
            {[
              { id: 'basic', label: 'Informations de base', icon: Info },
              { id: 'details', label: 'Détails', icon: Package },
              { id: 'variants', label: 'Variantes', icon: Palette },
              { id: 'preview', label: 'Aperçu', icon: Eye }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-6 py-4 text-sm font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'text-brand-600 bg-white border-b-2 border-brand-600 shadow-sm'
                    : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh] bg-neutral-50/30">
            {activeTab === 'basic' && (
              <div className="space-y-8">
                {/* Product Information Section */}
                <Card className="border-brand-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-display text-neutral-900 flex items-center gap-2">
                      <Info className="w-5 h-5 text-brand-500" />
                      Informations du produit
                    </CardTitle>
                    <CardDescription>
                      Renseignez les informations de base de votre produit
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium text-neutral-700">
                          Nom du produit *
                        </Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Ex: iPhone 15 Pro Max"
                          className={`transition-all duration-200 ${errors.name ? 'border-red-500 focus:border-red-500' : 'focus:border-brand-500'}`}
                        />
                        {errors.name && <p className="text-sm text-red-600 flex items-center gap-1">
                          <X className="w-3 h-3" />
                          {errors.name}
                        </p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="category" className="text-sm font-medium text-neutral-700">
                          Catégorie *
                        </Label>
                        <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                          <SelectTrigger className={`transition-all duration-200 ${errors.category ? 'border-red-500' : 'focus:border-brand-500'}`}>
                            <SelectValue placeholder="Choisissez une catégorie" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.category && <p className="text-sm text-red-600 flex items-center gap-1">
                          <X className="w-3 h-3" />
                          {errors.category}
                        </p>}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-sm font-medium text-neutral-700">
                        Description *
                      </Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Décrivez votre produit en détail : caractéristiques, avantages, utilisation..."
                        rows={4}
                        className={`transition-all duration-200 resize-none ${errors.description ? 'border-red-500 focus:border-red-500' : 'focus:border-brand-500'}`}
                  />
                      {errors.description && <p className="text-sm text-red-600 flex items-center gap-1">
                        <X className="w-3 h-3" />
                        {errors.description}
                      </p>}
                      <div className="text-xs text-neutral-500 mt-1">
                        {formData.description.length}/500 caractères
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Pricing & Inventory Section */}
                <Card className="border-brand-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-display text-neutral-900 flex items-center gap-2">
                      <Package className="w-5 h-5 text-brand-500" />
                      Prix et inventaire
                    </CardTitle>
                    <CardDescription>
                      Définissez le prix et gérez votre stock
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="price" className="text-sm font-medium text-neutral-700">
                          Prix de vente (DA) *
                        </Label>
                        <div className="relative">
                          <Input
                            id="price"
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.price}
                            onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                            placeholder="1,500.00"
                            className={`pr-12 transition-all duration-200 ${errors.price ? 'border-red-500 focus:border-red-500' : 'focus:border-brand-500'}`}
                          />
                          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-neutral-500 font-medium">
                            DA
                          </span>
                        </div>
                        {errors.price && <p className="text-sm text-red-600 flex items-center gap-1">
                          <X className="w-3 h-3" />
                          {errors.price}
                        </p>}
                        {formData.price && (
                          <p className="text-sm text-brand-600 font-medium">
                            Prix affiché: {Number(formData.price).toLocaleString('fr-DZ')} DA
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="stock" className="text-sm font-medium text-neutral-700">
                          Quantité en stock
                        </Label>
                        <Input
                          id="stock"
                          type="number"
                          min="0"
                          value={formData.stock}
                          onChange={(e) => setFormData(prev => ({ ...prev, stock: e.target.value }))}
                          placeholder="100"
                          className={`transition-all duration-200 ${errors.stock ? 'border-red-500 focus:border-red-500' : 'focus:border-brand-500'}`}
                        />
                        {errors.stock && <p className="text-sm text-red-600 flex items-center gap-1">
                          <X className="w-3 h-3" />
                          {errors.stock}
                        </p>}
                        <p className="text-xs text-neutral-500">
                          Laissez vide si vous ne gérez pas le stock
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Images Section */}
                <Card className="border-brand-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-display text-neutral-900 flex items-center gap-2">
                      <ImageIcon className="w-5 h-5 text-brand-500" />
                      Images du produit
                    </CardTitle>
                    <CardDescription>
                      Ajoutez jusqu'à 5 images de votre produit (max 5MB par image)
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Image Upload Area */}
                    <div
                      className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
                        isDragOver
                          ? 'border-brand-500 bg-brand-50'
                          : 'border-neutral-300 hover:border-brand-400 hover:bg-neutral-50'
                      }`}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                    >
                      <div className="space-y-4">
                        <div className="w-16 h-16 bg-brand-100 rounded-full flex items-center justify-center mx-auto">
                          <ImageIcon className="w-8 h-8 text-brand-500" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-neutral-900 mb-2">
                            Glissez-déposez vos images ici
                          </h3>
                          <p className="text-neutral-600 mb-4">
                            ou cliquez pour sélectionner des fichiers
                          </p>
                          <Button
                            type="button"
                            variant="outline"
                            className="border-brand-300 text-brand-600 hover:bg-brand-50"
                            onClick={() => document.getElementById('image-upload')?.click()}
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Choisir des images
                          </Button>
                          <input
                            id="image-upload"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleFileInputChange}
                            className="hidden"
                          />
                        </div>
                        <p className="text-xs text-neutral-500">
                          Formats acceptés: JPG, PNG, WebP • Taille max: 5MB par image
                        </p>
                      </div>
                    </div>

                    {/* Error Message */}
                    {errors.images && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <X className="w-3 h-3" />
                        {errors.images}
                      </p>
                    )}

                    {/* Image Previews */}
                    {imagePreviews.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="font-medium text-neutral-900">Images sélectionnées ({imagePreviews.length}/5)</h4>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                          {imagePreviews.map((preview, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={preview}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-24 object-cover rounded-lg border border-neutral-200 shadow-sm"
                              />
                              <button
                                type="button"
                                onClick={() => removeImage(index)}
                                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-red-600 shadow-lg"
                              >
                                <X className="w-3 h-3" />
                              </button>
                              {index === 0 && (
                                <Badge className="absolute bottom-1 left-1 text-xs bg-brand-500">
                                  Principal
                                </Badge>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'details' && (
              <div className="space-y-8">
                {/* Product Details Section */}
                <Card className="border-brand-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-display text-neutral-900 flex items-center gap-2">
                      <Package className="w-5 h-5 text-brand-500" />
                      Détails du produit
                    </CardTitle>
                    <CardDescription>
                      Informations supplémentaires pour la gestion et l'expédition
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="sku" className="text-sm font-medium text-neutral-700">
                          SKU / Référence produit
                        </Label>
                        <Input
                          id="sku"
                          value={formData.sku}
                          onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
                          placeholder="Ex: IPHONE-15-PRO-256"
                          className="transition-all duration-200 focus:border-brand-500"
                        />
                        <p className="text-xs text-neutral-500">
                          Code unique pour identifier votre produit
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="weight" className="text-sm font-medium text-neutral-700">
                          Poids (kg)
                        </Label>
                        <Input
                          id="weight"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.weight}
                          onChange={(e) => setFormData(prev => ({ ...prev, weight: e.target.value }))}
                          placeholder="0.50"
                          className={`transition-all duration-200 ${errors.weight ? 'border-red-500 focus:border-red-500' : 'focus:border-brand-500'}`}
                        />
                        {errors.weight && <p className="text-sm text-red-600 flex items-center gap-1">
                          <X className="w-3 h-3" />
                          {errors.weight}
                        </p>}
                        <p className="text-xs text-neutral-500">
                          Poids pour le calcul des frais d'expédition
                        </p>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-neutral-700">
                          Dimensions (cm)
                        </Label>
                        <p className="text-xs text-neutral-500 mt-1">
                          Dimensions pour l'emballage et l'expédition
                        </p>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="length" className="text-xs font-medium text-neutral-600">
                            Longueur
                          </Label>
                          <Input
                            id="length"
                            type="number"
                            min="0"
                            value={formData.dimensions.length}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              dimensions: { ...prev.dimensions, length: e.target.value }
                            }))}
                            placeholder="20"
                            className="transition-all duration-200 focus:border-brand-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="width" className="text-xs font-medium text-neutral-600">
                            Largeur
                          </Label>
                          <Input
                            id="width"
                            type="number"
                            min="0"
                            value={formData.dimensions.width}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              dimensions: { ...prev.dimensions, width: e.target.value }
                            }))}
                            placeholder="10"
                            className="transition-all duration-200 focus:border-brand-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="height" className="text-xs font-medium text-neutral-600">
                            Hauteur
                          </Label>
                          <Input
                            id="height"
                            type="number"
                            min="0"
                            value={formData.dimensions.height}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              dimensions: { ...prev.dimensions, height: e.target.value }
                            }))}
                            placeholder="5"
                            className="transition-all duration-200 focus:border-brand-500"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'variants' && (
              <div className="space-y-8">
                {/* Variants Section */}
                <Card className="border-brand-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-display text-neutral-900 flex items-center gap-2">
                      <Palette className="w-5 h-5 text-brand-500" />
                      Variantes du produit
                    </CardTitle>
                    <CardDescription>
                      Créez des variantes pour offrir différentes options (taille, couleur, matériau)
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {formData.variants.length === 0 ? (
                      <div className="text-center py-12 bg-neutral-50 rounded-lg border-2 border-dashed border-neutral-200">
                        <div className="w-16 h-16 bg-brand-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Palette className="w-8 h-8 text-brand-500" />
                        </div>
                        <h4 className="text-lg font-medium text-neutral-900 mb-2">Aucune variante configurée</h4>
                        <p className="text-neutral-500 mb-6 max-w-md mx-auto">
                          Les variantes permettent à vos clients de choisir entre différentes options comme la taille, la couleur ou le matériau
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {formData.variants.map((variant) => (
                          <Card key={variant.id} className="border-neutral-200">
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-base font-medium text-neutral-900 flex items-center gap-2">
                                  <Palette className="w-4 h-4 text-brand-500" />
                                  {variant.name}
                                </CardTitle>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeVariant(variant.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Minus className="w-4 h-4" />
                                  Supprimer
                                </Button>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                <p className="text-sm text-neutral-600">
                                  Sélectionnez les options disponibles pour cette variante :
                                </p>
                                <div className="flex flex-wrap gap-2">
                                  {variantTypes.find(vt => vt.name === variant.name)?.values.map((value) => (
                                    <Badge
                                      key={value}
                                      variant={variant.values.includes(value) ? "default" : "outline"}
                                      className={`cursor-pointer transition-all duration-200 ${
                                        variant.values.includes(value)
                                          ? 'bg-brand-500 hover:bg-brand-600 text-white'
                                          : 'hover:bg-brand-50 hover:text-brand-600 hover:border-brand-300'
                                      }`}
                                      onClick={() => {
                                        const newValues = variant.values.includes(value)
                                          ? variant.values.filter(v => v !== value)
                                          : [...variant.values, value];
                                        updateVariantValues(variant.id, newValues);
                                      }}
                                    >
                                      {value}
                                      {variant.values.includes(value) && (
                                        <X className="w-3 h-3 ml-1" />
                                      )}
                                    </Badge>
                                  ))}
                                </div>
                                {variant.values.length > 0 && (
                                  <p className="text-xs text-brand-600 font-medium">
                                    {variant.values.length} option(s) sélectionnée(s)
                                  </p>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}

                    <Separator />

                    <div className="space-y-3">
                      <h4 className="font-medium text-neutral-900">Ajouter une nouvelle variante</h4>
                      <div className="flex flex-wrap gap-3">
                        {variantTypes.map((variantType) => (
                          <Button
                            key={variantType.id}
                            variant="outline"
                            size="sm"
                            onClick={() => addVariant(variantType)}
                            disabled={formData.variants.some(v => v.name === variantType.name)}
                            className="flex items-center gap-2 border-brand-300 text-brand-600 hover:bg-brand-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Plus className="w-4 h-4" />
                            Ajouter {variantType.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'preview' && (
              <div className="space-y-8">
                {/* Product Preview Section */}
                <Card className="border-brand-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-display text-neutral-900 flex items-center gap-2">
                      <Eye className="w-5 h-5 text-brand-500" />
                      Aperçu du produit
                    </CardTitle>
                    <CardDescription>
                      Voici comment votre produit apparaîtra aux clients
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-white border border-neutral-200 rounded-lg p-6 shadow-sm">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Product Images */}
                        <div className="space-y-4">
                          {imagePreviews.length > 0 ? (
                            <div className="space-y-3">
                              <div className="aspect-square bg-neutral-50 rounded-lg overflow-hidden border border-neutral-200">
                                <img
                                  src={imagePreviews[0]}
                                  alt="Product preview"
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              {imagePreviews.length > 1 && (
                                <div className="flex gap-2 overflow-x-auto">
                                  {imagePreviews.slice(1, 5).map((preview, index) => (
                                    <img
                                      key={index}
                                      src={preview}
                                      alt={`Preview ${index + 2}`}
                                      className="w-16 h-16 object-cover rounded border border-neutral-200 flex-shrink-0"
                                    />
                                  ))}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="aspect-square bg-neutral-100 rounded-lg flex items-center justify-center border-2 border-dashed border-neutral-300">
                              <div className="text-center">
                                <ImageIcon className="w-12 h-12 text-neutral-400 mx-auto mb-2" />
                                <span className="text-neutral-500 text-sm">Aucune image</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Product Information */}
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-2xl font-bold text-neutral-900 mb-2">
                              {formData.name || 'Nom du produit'}
                            </h3>
                            <p className="text-sm text-neutral-500 uppercase tracking-wide">
                              {formData.category || 'Catégorie non définie'}
                            </p>
                          </div>

                          <div className="space-y-2">
                            <div className="text-3xl font-bold text-brand-600">
                              {formData.price ? `${Number(formData.price).toLocaleString('fr-DZ')} DA` : '0 DA'}
                            </div>
                            {formData.price && (
                              <p className="text-sm text-neutral-500">
                                Prix affiché aux clients
                              </p>
                            )}
                          </div>

                          <div className="prose prose-sm max-w-none">
                            <p className="text-neutral-700 leading-relaxed">
                              {formData.description || 'Aucune description fournie. Ajoutez une description détaillée pour aider vos clients à comprendre votre produit.'}
                            </p>
                          </div>

                          {/* Variants Display */}
                          {formData.variants.length > 0 && (
                            <div className="space-y-4">
                              <Separator />
                              <div>
                                <h4 className="font-medium text-neutral-900 mb-3">Options disponibles</h4>
                                <div className="space-y-3">
                                  {formData.variants.map((variant) => (
                                    <div key={variant.id} className="space-y-2">
                                      <Label className="text-sm font-medium text-neutral-700">
                                        {variant.name}
                                      </Label>
                                      <div className="flex flex-wrap gap-2">
                                        {variant.values.map((value) => (
                                          <Badge
                                            key={value}
                                            variant="outline"
                                            className="border-brand-300 text-brand-700 hover:bg-brand-50"
                                          >
                                            {value}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Product Details */}
                          <div className="space-y-3">
                            <Separator />
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              {formData.stock && (
                                <div className="flex items-center gap-2">
                                  <Package className="w-4 h-4 text-neutral-500" />
                                  <span className="text-neutral-600">Stock: {formData.stock}</span>
                                </div>
                              )}
                              {formData.sku && (
                                <div className="flex items-center gap-2">
                                  <Hash className="w-4 h-4 text-neutral-500" />
                                  <span className="text-neutral-600">SKU: {formData.sku}</span>
                                </div>
                              )}
                            </div>

                            <div className="flex items-center gap-3 pt-2">
                              <Badge
                                variant={formData.isActive ? "default" : "secondary"}
                                className={formData.isActive ? "bg-green-100 text-green-800" : ""}
                              >
                                {formData.isActive ? 'Produit actif' : 'Produit inactif'}
                              </Badge>
                              {formData.isDraft && (
                                <Badge variant="outline" className="border-amber-300 text-amber-700">
                                  Brouillon
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
            <div className="flex items-center justify-between p-6">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-3">
                  <Switch
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                    className="data-[state=checked]:bg-brand-500"
                  />
                  <div>
                    <Label className="text-sm font-medium text-neutral-900">
                      Produit actif
                    </Label>
                    <p className="text-xs text-neutral-500">
                      Le produit sera visible aux clients
                    </p>
                  </div>
                </div>

                {saveProgress > 0 && saveProgress < 100 && (
                  <div className="flex items-center gap-2">
                    <Progress value={saveProgress} className="w-24" />
                    <span className="text-xs text-neutral-500">{saveProgress}%</span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  onClick={onClose}
                  disabled={loading}
                  className="text-neutral-600 hover:text-neutral-900"
                >
                  Annuler
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleSubmit(true)}
                  disabled={loading}
                  className="border-neutral-300 text-neutral-700 hover:bg-neutral-50"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  {loading ? 'Sauvegarde...' : 'Sauvegarder en brouillon'}
                </Button>
                <Button
                  onClick={() => handleSubmit(false)}
                  disabled={loading}
                  className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Sauvegarde...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Publier le produit
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </>
  );
}
