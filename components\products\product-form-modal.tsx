'use client';

import React, { useState, useCallback } from 'react';

import { X, Upload, Plus, Minus, Save, Eye, FileText, Image as ImageIcon, Package, Info, Palette, Hash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';

interface ProductVariant {
  id: string;
  name: string;
  values: string[];
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  stock: string;
  sku: string;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  images: File[];
  variants: ProductVariant[];
  isDraft: boolean;
  isActive: boolean;
}

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProductFormData) => Promise<void>;
}

const categories = [
  'Électronique',
  'Vêtements',
  'Maison & Jardin',
  'Sports & Loisirs',
  'Beauté & Santé',
  'Livres',
  'Automobile',
  'Autres'
];

const variantTypes = [
  { id: 'size', name: 'Taille', values: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] },
  { id: 'color', name: 'Couleur', values: ['Rouge', 'Bleu', 'Vert', 'Noir', 'Blanc', 'Jaune'] },
  { id: 'material', name: 'Matériau', values: ['Coton', 'Polyester', 'Laine', 'Soie', 'Lin'] }
];

export default function ProductFormModal({ isOpen, onClose, onSubmit }: ProductFormModalProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: '',
    sku: '',
    weight: '',
    dimensions: {
      length: '',
      width: '',
      height: ''
    },
    images: [],
    variants: [],
    isDraft: false,
    isActive: true
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'details' | 'variants' | 'preview'>('basic');
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [saveProgress, setSaveProgress] = useState(0);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du produit est obligatoire';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Le nom doit contenir au moins 3 caractères';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Le prix est obligatoire';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Le prix doit être un nombre positif';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est obligatoire';
    } else if (formData.description.length < 10) {
      newErrors.description = 'La description doit contenir au moins 10 caractères';
    }

    if (!formData.category) {
      newErrors.category = 'La catégorie est obligatoire';
    }

    if (formData.stock && (isNaN(Number(formData.stock)) || Number(formData.stock) < 0)) {
      newErrors.stock = 'Le stock doit être un nombre positif ou zéro';
    }

    if (formData.weight && (isNaN(Number(formData.weight)) || Number(formData.weight) < 0)) {
      newErrors.weight = 'Le poids doit être un nombre positif';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleImageUpload = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB max
      return isValidType && isValidSize;
    });

    if (formData.images.length + validFiles.length > 5) {
      setErrors(prev => ({ ...prev, images: 'Vous ne pouvez ajouter que 5 images maximum' }));
      return;
    }

    if (validFiles.length !== fileArray.length) {
      setErrors(prev => ({ ...prev, images: 'Certains fichiers ne sont pas valides (format ou taille)' }));
    }

    const newImages = [...formData.images, ...validFiles];
    setFormData(prev => ({ ...prev, images: newImages }));

    const newPreviews = validFiles.map(file => URL.createObjectURL(file));
    setImagePreviews(prev => [...prev, ...newPreviews]);

    setErrors(prev => ({ ...prev, images: '' }));
  }, [formData.images]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleImageUpload(e.target.files);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    if (e.dataTransfer.files) {
      handleImageUpload(e.dataTransfer.files);
    }
  }, [handleImageUpload]);

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);

    setFormData(prev => ({ ...prev, images: newImages }));
    setImagePreviews(newPreviews);
  };

  const addVariant = (variantType: typeof variantTypes[0]) => {
    const newVariant: ProductVariant = {
      id: Date.now().toString(),
      name: variantType.name,
      values: []
    };
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  const removeVariant = (variantId: string) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter(v => v.id !== variantId)
    }));
  };

  const updateVariantValues = (variantId: string, values: string[]) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map(v => 
        v.id === variantId ? { ...v, values } : v
      )
    }));
  };

  const getFormattedPrice = () => {
    const price = Number(formData.price) || 0;
    return price;
  };

  const handleSubmit = async (isDraft: boolean = false) => {
    if (!isDraft && !validateForm()) {
      return;
    }

    setLoading(true);
    setSaveProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setSaveProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await onSubmit({ ...formData, isDraft });

      clearInterval(progressInterval);
      setSaveProgress(100);

      setTimeout(() => {
        onClose();
        setFormData({
          name: '',
          description: '',
          price: '',
          category: '',
          stock: '',
          sku: '',
          weight: '',
          dimensions: { length: '', width: '', height: '' },
          images: [],
          variants: [],
          isDraft: false,
          isActive: true
        });
        setImagePreviews([]);
        setErrors({});
        setSaveProgress(0);
      }, 500);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setErrors(prev => ({ ...prev, submit: 'Erreur lors de la sauvegarde du produit' }));
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div>
      <p>Test Modal</p>
    </div>
  );
}
