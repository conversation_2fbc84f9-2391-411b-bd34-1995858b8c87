'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Package, Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Inbox, ShoppingBag, Eye, ToggleLeft, ToggleRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import ProductFormModal from '@/components/products/product-form-modal';
import { useProducts } from '@/hooks/use-products';
import { toast } from 'sonner';

export default function ProductsPage() {
  const { products, loading, addProduct, deleteProduct, toggleProductStatus, searchProducts, getProductStats } = useProducts();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(products);

  const stats = getProductStats();

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      setFilteredProducts(searchProducts(query));
    } else {
      setFilteredProducts(products);
    }
  };

  const handleAddProduct = async (formData: any) => {
    try {
      await addProduct(formData);
      toast.success(formData.isDraft ? 'Produit sauvegardé en brouillon' : 'Produit ajouté avec succès');
      setIsModalOpen(false);
    } catch (error) {
      toast.error('Erreur lors de l\'ajout du produit');
    }
  };

  const handleDeleteProduct = async (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      try {
        await deleteProduct(id);
        toast.success('Produit supprimé avec succès');
      } catch (error) {
        toast.error('Erreur lors de la suppression');
      }
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      await toggleProductStatus(id);
      toast.success('Statut du produit mis à jour');
    } catch (error) {
      toast.error('Erreur lors de la mise à jour');
    }
  };

  React.useEffect(() => {
    setFilteredProducts(products);
  }, [products]);

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600">Total Produits</p>
              <p className="text-2xl font-bold text-neutral-900">{stats.total}</p>
            </div>
            <div className="w-12 h-12 bg-brand-100 rounded-lg flex items-center justify-center">
              <Package className="w-6 h-6 text-brand-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600">Produits Actifs</p>
              <p className="text-2xl font-bold text-emerald-600">{stats.active}</p>
            </div>
            <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-emerald-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600">Brouillons</p>
              <p className="text-2xl font-bold text-orange-600">{stats.drafts}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Edit className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600">Stock Faible</p>
              <p className="text-2xl font-bold text-red-600">{stats.lowStock}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Package className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Header Actions */}
      <motion.div
        className="flex justify-end mb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <Button
          onClick={() => setIsModalOpen(true)}
          className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Plus className="w-4 h-4 mr-2" />
          Ajouter un produit
        </Button>
      </motion.div>

      {/* Filters */}
      <motion.div
        className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Rechercher un produit..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filtres
          </Button>
        </div>
      </motion.div>

      {/* Products Table */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.6 }}
      >
        {filteredProducts.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Inbox className="w-10 h-10 text-neutral-400" />
            </div>
            <h3 className="text-lg font-semibold text-neutral-900 mb-2">Aucun produit dans votre catalogue</h3>
            <p className="text-neutral-500 mb-6 max-w-md mx-auto">
              Commencez par ajouter vos premiers produits pour démarrer votre boutique en ligne
            </p>
            <Button
              onClick={() => setIsModalOpen(true)}
              className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Ajouter votre premier produit
            </Button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-neutral-50 border-b border-neutral-200">
                  <tr>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Produit</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Catégorie</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Prix</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Stock</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Statut</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product, index) => (
                    <motion.tr
                      key={product.id}
                      className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-neutral-200 rounded-lg flex items-center justify-center overflow-hidden">
                            {product.images && product.images.length > 0 ? (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <Package className="w-6 h-6 text-neutral-400" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-neutral-900">{product.name}</p>
                            <p className="text-sm text-neutral-500">
                              {product.sku ? `SKU: ${product.sku}` : `ID: ${product.id}`}
                            </p>
                            {product.isDraft && (
                              <Badge variant="outline" className="text-xs mt-1">
                                Brouillon
                              </Badge>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-neutral-700">{product.category}</td>
                      <td className="py-4 px-6 font-medium text-neutral-900">
                        {(product.price * (1 + product.taxRate / 100)).toFixed(2)} DA
                        <p className="text-xs text-neutral-500">TTC</p>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`font-medium ${
                          product.stock === 0
                            ? 'text-red-600'
                            : product.stock < 10
                            ? 'text-orange-600'
                            : 'text-emerald-600'
                        }`}>
                          {product.stock}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={product.isActive ? "default" : "secondary"}
                            className={product.isActive ? "bg-emerald-100 text-emerald-700" : "bg-neutral-100 text-neutral-700"}
                          >
                            {product.isActive ? 'Actif' : 'Inactif'}
                          </Badge>
                          {product.variants && product.variants.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {product.variants.length} variante{product.variants.length > 1 ? 's' : ''}
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-blue-50 hover:text-blue-600"
                            title="Voir le produit"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-blue-50 hover:text-blue-600"
                            title="Modifier"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStatus(product.id)}
                            className="hover:bg-orange-50 hover:text-orange-600"
                            title={product.isActive ? "Désactiver" : "Activer"}
                          >
                            {product.isActive ? (
                              <ToggleRight className="w-4 h-4" />
                            ) : (
                              <ToggleLeft className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteProduct(product.id)}
                            className="hover:bg-red-50 hover:text-red-600"
                            title="Supprimer"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between p-6 border-t border-neutral-100 bg-neutral-50">
              <p className="text-sm text-neutral-600">
                Affichage de 1 à {filteredProducts.length} sur {filteredProducts.length} produit{filteredProducts.length > 1 ? 's' : ''}
              </p>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  Précédent
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Suivant
                </Button>
              </div>
            </div>
          </>
        )}
      </motion.div>

      {/* Product Form Modal */}
      <ProductFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddProduct}
      />
    </div>
  );
}
